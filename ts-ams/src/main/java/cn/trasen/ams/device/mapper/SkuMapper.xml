<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.ams.device.dao.SkuMapper">
    <resultMap id="BaseResultMap" type="cn.trasen.ams.device.model.Sku">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="model" jdbcType="VARCHAR" property="model"/>
        <result column="brand_id" jdbcType="VARCHAR" property="brandId"/>
        <result column="manufacturer_id" jdbcType="VARCHAR" property="manufacturerId"/>
        <result column="category22_id" jdbcType="VARCHAR" property="category22Id"/>
        <result column="lifespan_val" jdbcType="INTEGER" property="lifespanVal"/>
        <result column="lifespan_unit" jdbcType="CHAR" property="lifespanUnit"/>
        <result column="calibration_type" jdbcType="CHAR" property="calibrationType"/>
        <result column="calibration_cycle_val" jdbcType="INTEGER" property="calibrationCycleVal"/>
        <result column="calibration_cycle_unit" jdbcType="CHAR" property="calibrationCycleUnit"/>
        <result column="maint_cycle_val" jdbcType="INTEGER" property="maintCycleVal"/>
        <result column="maint_cycle_unit" jdbcType="CHAR" property="maintCycleUnit"/>
        <result column="is_life_support" jdbcType="CHAR" property="isLifeSupport"/>
        <result column="is_special" jdbcType="CHAR" property="isSpecial"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName"/>
        <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode"/>
        <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName"/>
        <result column="is_deleted" jdbcType="CHAR" property="isDeleted"/>
    </resultMap>
    <sql id="getListSql">
        select
        t1.*,
        t2.name as brand_name,
        t3.name as manufacturer_name,
        t4.name as category22_name,
        t5.name as category_name
        from d_sku as t1
        left join c_brand as t2 on t1.brand_id = t2.id
        left join c_manufacturer as t3 on t1.manufacturer_id = t3.id
        left join d_category22 as t4 on t1.category22_id = t4.id
        left join c_category as t5 on t1.category_id = t5.id
        where t1.`is_deleted` = 'N'
        <if test="name != null and name != ''">
            and (
            t1.name LIKE CONCAT('%', #{name}, '%')
            OR t1.sp LIKE CONCAT('%', #{name}, '%')
            OR t1.qp LIKE CONCAT('%', #{name}, '%')
            )
        </if>
        <if test="model != null and model != ''">
            and t1.`model` like concat('%', #{model}, '%')
        </if>
        <if test="brandId != null and brandId != ''">
            and t1.`brand_id` = #{brandId}
        </if>
        <if test="manufacturerId != null and manufacturerId != ''">
            and t1.`manufacturer_id` = #{manufacturerId}
        </if>
        <if test="skuType != null and skuType != ''">
            and t1.sku_type = #{skuType}
        </if>
        <if test="category22Id != null and category22Id != ''">
            and t4.tree_ids like concat('%', #{category22Id}, '%')
        </if>
        <if test="categoryId != null and categoryId != ''">
            and t5.tree_ids like concat('%', #{categoryId}, '%')
        </if>
        <if test="ignoreIds != null and ignoreIds.size() > 0">
            and t1.`id` not in
            <foreach item="id" collection="ignoreIds" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <choose>
            <when test="selectedIds != null and selectedIds.size() > 0">
                order by
                CASE
                WHEN t1.id IN
                <foreach item="id" collection="selectedIds" open="(" separator="," close=")">
                    #{id}
                </foreach>
                THEN 0
                ELSE 1
                END,
                t1.`create_date` desc
            </when>
            <otherwise>
                order by t1.`create_date` desc
            </otherwise>
        </choose>
    </sql>
    <select id="getList" parameterType="cn.trasen.ams.device.model.Sku" resultType="cn.trasen.ams.device.model.Sku"
            resultMap="BaseResultMap">
        <include refid="getListSql"/>
    </select>
    <select id="getListNoPage" parameterType="cn.trasen.ams.device.model.Sku"
            resultType="cn.trasen.ams.device.model.Sku"
            resultMap="BaseResultMap">
        <include refid="getListSql"/>
    </select>
    <select id="selectOnById" resultType="cn.trasen.ams.device.model.Sku">
        select t1.*,
               t2.name as brand_name,
               t3.name as manufacturer_name,
               t4.name as category22_name,
               t5.name as category2_name
        from d_sku as t1
                 left join c_brand as t2 on t1.brand_id = t2.id
                 left join c_manufacturer as t3 on t1.manufacturer_id = t3.id
                 left join d_category22 as t4 on t1.category22_id = t4.id
                 left join c_category as t5 on t1.category_id = t5.id
        where t1.`is_deleted` = 'N'
          and t1.`id` = #{id} limit 1
    </select>
    <insert id="batchInsert">
        <![CDATA[
			INSERT INTO d_sku
			(
				id,
                name,
                sp,
			    qp,
			    sku_type,
			    need_install,
			    unit,
                model,
                brand_id,
                manufacturer_id,
			    category_id,
                category22_id,
                lifespan_val,
                lifespan_unit,
                calibration_type,
                calibration_cycle_val,
                calibration_cycle_unit,
                maint_cycle_val,
                maint_cycle_unit,
                is_life_support,
                is_special,
				create_date,
				create_user,
				create_user_name,
				update_date,
				update_user,
				update_user_name,
			    sso_org_code,
			    sso_org_name,
				is_deleted,
                dept_id,
                dept_name
			)
			VALUES
		]]>
        <foreach collection="list" item="item" index="index" separator=",">
            <![CDATA[
			(
				#{item.id},
				#{item.name},
			    #{item.sp},
			    #{item.qp},
			    #{item.skuType},
			    #{item.needInstall},
			    #{item.unit},
                #{item.model},
                #{item.brandId},
                #{item.manufacturerId},
			    #{item.categoryId},
                #{item.category22Id},
                #{item.lifespanVal},
                #{item.lifespanUnit},
                #{item.calibrationType},
                #{item.calibrationCycleVal},
                #{item.calibrationCycleUnit},
                #{item.maintCycleVal},
                #{item.maintCycleUnit},
                #{item.isLifeSupport},
                #{item.isSpecial},
				#{item.createDate},
				#{item.createUser},
				#{item.createUserName},
				#{item.updateDate},
				#{item.updateUserName},
			    #{item.ssoOrgCode},
				#{item.ssoOrgName},
				#{item.isDeleted},
				#{item.deptId},
				#{item.deptName}
			)
			]]>
        </foreach>
    </insert>
    <select id="searchSkuList" resultType="cn.trasen.ams.device.model.Sku">

        SELECT *,t2.name AS brand_name, t3.name AS manufacturer_name,
        CASE
        WHEN t1.id = #{selectedId} THEN 1000000
        ELSE MATCH (t1.`name`, t1.`model`) AGAINST(#{keyword} IN NATURAL LANGUAGE MODE)
        END AS relevance_score
        FROM d_sku t1
        LEFT JOIN c_brand t2 ON t1.brand_id = t2.id
        LEFT JOIN c_manufacturer t3 ON t1.manufacturer_id = t3.id
        WHERE t1.`is_deleted` = 'N' AND t1.`sku_type` = #{skuType}
        <if test="keyword != null and keyword != ''">
            AND MATCH (t1.`name`, t1.`model`) AGAINST(#{keyword} IN NATURAL LANGUAGE MODE)
        </if>
        <if test="selectedId != null and selectedId != ''">
            OR t1.id = #{selectedId}
        </if>
        ORDER BY relevance_score DESC
        LIMIT 3;
    </select>
</mapper>
