package cn.trasen.ams.common.interceptor.enhanced;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

/**
 * 性能监控类
 * 
 * <AUTHOR>
 * @date 2025/9/4
 */
public class PerformanceMonitor {
    
    private static final Logger log = LoggerFactory.getLogger(PerformanceMonitor.class);
    
    // 统计信息
    private static final LongAdder totalRequests = new LongAdder();
    private static final LongAdder successfulModifications = new LongAdder();
    private static final LongAdder failedParsing = new LongAdder();
    private static final LongAdder cacheHits = new LongAdder();
    private static final LongAdder cacheMisses = new LongAdder();
    private static final AtomicLong totalExecutionTime = new AtomicLong(0);
    private static final AtomicLong maxExecutionTime = new AtomicLong(0);
    private static final AtomicLong minExecutionTime = new AtomicLong(Long.MAX_VALUE);
    
    /**
     * 记录请求
     */
    public static void recordRequest() {
        totalRequests.increment();
    }
    
    /**
     * 记录成功修改
     */
    public static void recordSuccessfulModification() {
        successfulModifications.increment();
    }
    
    /**
     * 记录解析失败
     */
    public static void recordFailedParsing() {
        failedParsing.increment();
    }
    
    /**
     * 记录缓存命中
     */
    public static void recordCacheHit() {
        cacheHits.increment();
    }
    
    /**
     * 记录缓存未命中
     */
    public static void recordCacheMiss() {
        cacheMisses.increment();
    }
    
    /**
     * 记录执行时间
     */
    public static void recordExecutionTime(long executionTime) {
        totalExecutionTime.addAndGet(executionTime);
        
        // 更新最大执行时间
        long currentMax = maxExecutionTime.get();
        while (executionTime > currentMax) {
            if (maxExecutionTime.compareAndSet(currentMax, executionTime)) {
                break;
            }
            currentMax = maxExecutionTime.get();
        }
        
        // 更新最小执行时间
        long currentMin = minExecutionTime.get();
        while (executionTime < currentMin) {
            if (minExecutionTime.compareAndSet(currentMin, executionTime)) {
                break;
            }
            currentMin = minExecutionTime.get();
        }
    }
    
    /**
     * 获取统计信息
     */
    public static PerformanceStats getStats() {
        long requests = totalRequests.sum();
        long successful = successfulModifications.sum();
        long failed = failedParsing.sum();
        long hits = cacheHits.sum();
        long misses = cacheMisses.sum();
        long totalTime = totalExecutionTime.get();
        long maxTime = maxExecutionTime.get();
        long minTime = minExecutionTime.get() == Long.MAX_VALUE ? 0 : minExecutionTime.get();
        
        double avgTime = requests > 0 ? (double) totalTime / requests : 0;
        double successRate = requests > 0 ? (double) successful / requests * 100 : 0;
        double cacheHitRate = (hits + misses) > 0 ? (double) hits / (hits + misses) * 100 : 0;
        
        return new PerformanceStats(
                requests, successful, failed, hits, misses,
                totalTime, avgTime, maxTime, minTime,
                successRate, cacheHitRate
        );
    }
    
    /**
     * 重置统计信息
     */
    public static void reset() {
        totalRequests.reset();
        successfulModifications.reset();
        failedParsing.reset();
        cacheHits.reset();
        cacheMisses.reset();
        totalExecutionTime.set(0);
        maxExecutionTime.set(0);
        minExecutionTime.set(Long.MAX_VALUE);
    }
    
    /**
     * 打印统计信息
     */
    public static void printStats() {
        PerformanceStats stats = getStats();
        log.info("=== 拦截器性能统计 ===");
        log.info("总请求数: {}", stats.getTotalRequests());
        log.info("成功修改数: {}", stats.getSuccessfulModifications());
        log.info("解析失败数: {}", stats.getFailedParsing());
        log.info("缓存命中数: {}", stats.getCacheHits());
        log.info("缓存未命中数: {}", stats.getCacheMisses());
        log.info("总执行时间: {}ms", stats.getTotalExecutionTime());
        log.info("平均执行时间: {:.2f}ms", stats.getAverageExecutionTime());
        log.info("最大执行时间: {}ms", stats.getMaxExecutionTime());
        log.info("最小执行时间: {}ms", stats.getMinExecutionTime());
        log.info("成功率: {:.2f}%", stats.getSuccessRate());
        log.info("缓存命中率: {:.2f}%", stats.getCacheHitRate());
        log.info("========================");
    }
    
    /**
     * 性能统计数据类
     */
    public static class PerformanceStats {
        private final long totalRequests;
        private final long successfulModifications;
        private final long failedParsing;
        private final long cacheHits;
        private final long cacheMisses;
        private final long totalExecutionTime;
        private final double averageExecutionTime;
        private final long maxExecutionTime;
        private final long minExecutionTime;
        private final double successRate;
        private final double cacheHitRate;
        
        public PerformanceStats(long totalRequests, long successfulModifications, long failedParsing,
                              long cacheHits, long cacheMisses, long totalExecutionTime,
                              double averageExecutionTime, long maxExecutionTime, long minExecutionTime,
                              double successRate, double cacheHitRate) {
            this.totalRequests = totalRequests;
            this.successfulModifications = successfulModifications;
            this.failedParsing = failedParsing;
            this.cacheHits = cacheHits;
            this.cacheMisses = cacheMisses;
            this.totalExecutionTime = totalExecutionTime;
            this.averageExecutionTime = averageExecutionTime;
            this.maxExecutionTime = maxExecutionTime;
            this.minExecutionTime = minExecutionTime;
            this.successRate = successRate;
            this.cacheHitRate = cacheHitRate;
        }
        
        // Getters
        public long getTotalRequests() { return totalRequests; }
        public long getSuccessfulModifications() { return successfulModifications; }
        public long getFailedParsing() { return failedParsing; }
        public long getCacheHits() { return cacheHits; }
        public long getCacheMisses() { return cacheMisses; }
        public long getTotalExecutionTime() { return totalExecutionTime; }
        public double getAverageExecutionTime() { return averageExecutionTime; }
        public long getMaxExecutionTime() { return maxExecutionTime; }
        public long getMinExecutionTime() { return minExecutionTime; }
        public double getSuccessRate() { return successRate; }
        public double getCacheHitRate() { return cacheHitRate; }
    }
}
