package cn.trasen.ams.common.interceptor.enhanced;

/**
 * SQL解析结果类
 * 
 * <AUTHOR>
 * @date 2025/9/4
 */
public class SqlParseResult {
    
    private final boolean success;
    private final String modifiedSql;
    private final String mainTable;
    private final String errorMessage;
    private final String message;
    
    private SqlParseResult(boolean success, String modifiedSql, String mainTable, String message, String errorMessage) {
        this.success = success;
        this.modifiedSql = modifiedSql;
        this.mainTable = mainTable;
        this.message = message;
        this.errorMessage = errorMessage;
    }
    
    /**
     * 创建成功结果
     */
    public static SqlParseResult success(String modifiedSql, String mainTable, String message) {
        return new SqlParseResult(true, modifiedSql, mainTable, message, null);
    }
    
    /**
     * 创建失败结果
     */
    public static SqlParseResult failure(String originalSql, String errorMessage) {
        return new SqlParseResult(false, originalSql, null, null, errorMessage);
    }
    
    public boolean isSuccess() {
        return success;
    }
    
    public String getModifiedSql() {
        return modifiedSql;
    }
    
    public String getMainTable() {
        return mainTable;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public String getMessage() {
        return message;
    }
    
    @Override
    public String toString() {
        return "SqlParseResult{" +
                "success=" + success +
                ", modifiedSql='" + modifiedSql + '\'' +
                ", mainTable='" + mainTable + '\'' +
                ", message='" + message + '\'' +
                ", errorMessage='" + errorMessage + '\'' +
                '}';
    }
}
