package cn.trasen.ams.common.interceptor.enhanced;

import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.StringValue;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.expression.operators.relational.EqualsTo;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.Select;
import net.sf.jsqlparser.statement.select.SelectBody;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.SetOperationList;
import net.sf.jsqlparser.statement.select.FromItem;
import net.sf.jsqlparser.statement.select.SubSelect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * SQL解析器测试类
 * 用于测试各种复杂SQL语句的解析和修改功能
 * 
 * <AUTHOR>
 * @date 2025/9/4
 */
public class SqlParserTest {
    
    private static final Logger log = LoggerFactory.getLogger(SqlParserTest.class);
    
    public static void main(String[] args) {
        SqlParserTest test = new SqlParserTest();
        test.runAllTests();
    }
    
    public void runAllTests() {
        log.info("开始SQL解析器测试...");
        
        // 基本SELECT测试
        testBasicSelect();
        
        // 子查询测试
        testSubQuery();
        
        // JOIN测试
        testJoinQuery();
        
        // UNION测试
        testUnionQuery();
        
        // 复杂嵌套查询测试
        testComplexNestedQuery();
        
        log.info("SQL解析器测试完成!");
    }
    
    /**
     * 测试基本SELECT语句
     */
    private void testBasicSelect() {
        log.info("=== 测试基本SELECT语句 ===");
        
        String[] testSqls = {
            "SELECT * FROM d_user",
            "SELECT * FROM d_user u",
            "SELECT * FROM d_user AS u",
            "SELECT * FROM d_user WHERE name = 'test'",
            "SELECT * FROM d_user u WHERE u.name = 'test'",
            "SELECT * FROM schema.d_user",
            "SELECT * FROM `d_user`",
            "SELECT id, name FROM d_user ORDER BY id"
        };
        
        for (String sql : testSqls) {
            testSqlModification(sql, "ORG001");
        }
    }
    
    /**
     * 测试子查询
     */
    private void testSubQuery() {
        log.info("=== 测试子查询 ===");
        
        String[] testSqls = {
            "SELECT * FROM (SELECT * FROM d_user) t",
            "SELECT * FROM d_user WHERE id IN (SELECT user_id FROM d_role)",
            "SELECT * FROM d_user u WHERE EXISTS (SELECT 1 FROM d_role r WHERE r.user_id = u.id)"
        };
        
        for (String sql : testSqls) {
            testSqlModification(sql, "ORG001");
        }
    }
    
    /**
     * 测试JOIN查询
     */
    private void testJoinQuery() {
        log.info("=== 测试JOIN查询 ===");
        
        String[] testSqls = {
            "SELECT * FROM d_user u JOIN d_role r ON u.id = r.user_id",
            "SELECT * FROM d_user u LEFT JOIN d_role r ON u.id = r.user_id",
            "SELECT * FROM d_user u INNER JOIN d_role r ON u.id = r.user_id"
        };
        
        for (String sql : testSqls) {
            testSqlModification(sql, "ORG001");
        }
    }
    
    /**
     * 测试UNION查询
     */
    private void testUnionQuery() {
        log.info("=== 测试UNION查询 ===");
        
        String[] testSqls = {
            "SELECT id, name FROM d_user UNION SELECT id, name FROM d_admin",
            "SELECT id, name FROM d_user WHERE status = 1 UNION ALL SELECT id, name FROM d_admin WHERE status = 1"
        };
        
        for (String sql : testSqls) {
            testSqlModification(sql, "ORG001");
        }
    }
    
    /**
     * 测试复杂嵌套查询
     */
    private void testComplexNestedQuery() {
        log.info("=== 测试复杂嵌套查询 ===");
        
        String[] testSqls = {
            "SELECT u.*, (SELECT COUNT(*) FROM d_role r WHERE r.user_id = u.id) as role_count FROM d_user u",
            "SELECT * FROM d_user u WHERE u.id IN (SELECT DISTINCT user_id FROM d_role WHERE role_name LIKE '%admin%')"
        };
        
        for (String sql : testSqls) {
            testSqlModification(sql, "ORG001");
        }
    }
    
    /**
     * 测试SQL修改
     */
    private void testSqlModification(String originalSql, String orgCode) {
        try {
            log.info("原始SQL: {}", originalSql);
            
            Statement statement = CCJSqlParserUtil.parse(originalSql);
            
            if (statement instanceof Select) {
                Select select = (Select) statement;
                SelectBody selectBody = select.getSelectBody();
                
                boolean modified = processSelectBody(selectBody, orgCode);
                
                if (modified) {
                    String modifiedSql = select.toString();
                    log.info("修改后SQL: {}", modifiedSql);
                } else {
                    log.info("无需修改");
                }
            } else {
                log.info("非SELECT语句，跳过");
            }
            
        } catch (JSQLParserException e) {
            log.error("SQL解析失败: {}", e.getMessage());
        } catch (Exception e) {
            log.error("处理SQL时发生异常", e);
        }
        
        log.info("---");
    }
    
    /**
     * 处理SelectBody（简化版本，仅用于测试）
     */
    private boolean processSelectBody(SelectBody selectBody, String orgCode) {
        if (selectBody instanceof PlainSelect) {
            return processPlainSelect((PlainSelect) selectBody, orgCode);
        } else if (selectBody instanceof SetOperationList) {
            return processSetOperationList((SetOperationList) selectBody, orgCode);
        }
        return false;
    }
    
    /**
     * 处理普通SELECT语句（简化版本）
     */
    private boolean processPlainSelect(PlainSelect plainSelect, String orgCode) {
        FromItem fromItem = plainSelect.getFromItem();
        if (fromItem == null) {
            return false;
        }

        boolean modified = false;

        // 处理主表
        if (fromItem instanceof Table) {
            Table table = (Table) fromItem;
            if (shouldAddOrgCodeCondition(table.getName())) {
                addOrgCodeCondition(plainSelect, table, orgCode);
                modified = true;
            }
        } else if (fromItem instanceof SubSelect) {
            // 递归处理子查询
            SubSelect subSelect = (SubSelect) fromItem;
            modified = processSelectBody(subSelect.getSelectBody(), orgCode);
        }

        return modified;
    }
    
    /**
     * 处理UNION等集合操作（简化版本）
     */
    private boolean processSetOperationList(SetOperationList setOperationList, String orgCode) {
        boolean modified = false;
        List<SelectBody> selects = setOperationList.getSelects();
        if (selects != null) {
            for (SelectBody selectBody : selects) {
                if (processSelectBody(selectBody, orgCode)) {
                    modified = true;
                }
            }
        }
        return modified;
    }
    
    /**
     * 添加机构代码条件（简化版本）
     */
    private void addOrgCodeCondition(PlainSelect plainSelect, Table table, String orgCode) {
        String tableAlias = table.getAlias() != null ? table.getAlias().getName() : table.getName();
        Column orgCodeColumn = new Column(tableAlias + ".sso_org_code");
        StringValue orgCodeValue = new StringValue(orgCode);
        
        EqualsTo equalsTo = new EqualsTo();
        equalsTo.setLeftExpression(orgCodeColumn);
        equalsTo.setRightExpression(orgCodeValue);

        Expression where = plainSelect.getWhere();
        if (where == null) {
            plainSelect.setWhere(equalsTo);
        } else {
            AndExpression andExpression = new AndExpression(equalsTo, where);
            plainSelect.setWhere(andExpression);
        }
    }
    
    /**
     * 判断是否需要添加机构代码条件（简化版本）
     */
    private boolean shouldAddOrgCodeCondition(String tableName) {
        if (tableName == null || tableName.trim().isEmpty()) {
            return false;
        }
        
        String pureTableName = tableName.toLowerCase();
        if (pureTableName.contains(".")) {
            String[] parts = pureTableName.split("\\.");
            pureTableName = parts[parts.length - 1];
        }
        
        return pureTableName.startsWith("d_") || pureTableName.startsWith("m_") || 
               pureTableName.startsWith("t_") || pureTableName.startsWith("c_");
    }
}
