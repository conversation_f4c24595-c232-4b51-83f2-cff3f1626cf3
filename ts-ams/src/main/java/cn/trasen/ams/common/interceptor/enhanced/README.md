# 增强版机构代码拦截器

## 概述

增强版机构代码拦截器使用专业的JSQLParser库来解析和修改SQL语句，相比原版拦截器具有更强的健壮性和更好的性能。

## 主要特性

### 1. 专业SQL解析
- 使用JSQLParser库进行SQL解析，支持标准SQL语法
- 正确处理复杂的SQL结构，包括：
  - 子查询（嵌套查询）
  - UNION/UNION ALL查询
  - 复杂JOIN查询（INNER JOIN, LEFT JOIN, RIGHT JOIN等）
  - CTE（WITH子句）
  - 复杂的WHERE条件

### 2. 性能优化
- **缓存机制**：缓存SQL解析结果，避免重复解析相同的SQL
- **智能检测**：只对需要的表添加机构代码条件
- **快速失败**：解析失败时快速回退到原始SQL

### 3. 健壮性保证
- **完善的错误处理**：解析失败时不影响原始SQL执行
- **回退机制**：任何异常情况下都能保证系统正常运行
- **日志记录**：详细的日志记录便于问题排查

### 4. 灵活配置
- **表过滤规则**：支持配置需要包含/排除的表
- **性能监控**：可选的性能监控功能
- **缓存控制**：可配置缓存大小和启用/禁用

## 与原版拦截器的对比

| 特性 | 原版拦截器 | 增强版拦截器 |
|------|------------|--------------|
| SQL解析方式 | 正则表达式 | JSQLParser专业库 |
| 子查询支持 | 有限支持 | 完全支持 |
| UNION查询 | 不支持 | 完全支持 |
| 复杂JOIN | 有限支持 | 完全支持 |
| CTE支持 | 不支持 | 完全支持 |
| 性能缓存 | 无 | 有 |
| 错误处理 | 基础 | 完善 |
| 配置灵活性 | 有限 | 高度灵活 |

## 使用方法

### 1. 添加依赖

在pom.xml中添加JSQLParser依赖：

```xml
<dependency>
    <groupId>com.github.jsqlparser</groupId>
    <artifactId>jsqlparser</artifactId>
    <version>4.7</version>
</dependency>
```

### 2. 配置拦截器

在MyBatis配置中注册拦截器：

```xml
<plugins>
    <plugin interceptor="cn.trasen.ams.common.interceptor.enhanced.EnhancedSsoOrgCodeInterceptor">
        <property name="enableSqlModification" value="true"/>
        <property name="enableLogging" value="true"/>
        <property name="enableCache" value="true"/>
    </plugin>
</plugins>
```

### 3. 应用配置

在application.yml中配置：

```yaml
trasen:
  interceptor:
    org-code:
      enable-sql-modification: true
      enable-logging: true
      enable-cache: true
      max-cache-size: 1000
      enable-performance-monitoring: false
      performance-threshold: 100
      exclude-tables:
        - sys_config
        - sys_log
        - d_category22
      include-table-prefixes:
        - d_
        - m_
        - t_
        - c_
```

## 支持的SQL类型

### 1. 基本SELECT查询
```sql
-- 原始SQL
SELECT * FROM d_user WHERE status = 1

-- 修改后SQL
SELECT * FROM d_user WHERE d_user.sso_org_code = 'ORG001' AND status = 1
```

### 2. 带别名的查询
```sql
-- 原始SQL
SELECT * FROM d_user u WHERE u.status = 1

-- 修改后SQL
SELECT * FROM d_user u WHERE u.sso_org_code = 'ORG001' AND u.status = 1
```

### 3. JOIN查询
```sql
-- 原始SQL
SELECT * FROM d_user u JOIN d_role r ON u.id = r.user_id

-- 修改后SQL
SELECT * FROM d_user u JOIN d_role r ON u.sso_org_code = 'ORG001' AND u.id = r.user_id AND r.sso_org_code = 'ORG001'
```

### 4. 子查询
```sql
-- 原始SQL
SELECT * FROM d_user WHERE id IN (SELECT user_id FROM d_role WHERE role_name = 'admin')

-- 修改后SQL
SELECT * FROM d_user WHERE d_user.sso_org_code = 'ORG001' AND id IN (SELECT user_id FROM d_role WHERE d_role.sso_org_code = 'ORG001' AND role_name = 'admin')
```

### 5. UNION查询
```sql
-- 原始SQL
SELECT id, name FROM d_user UNION SELECT id, name FROM d_admin

-- 修改后SQL
SELECT id, name FROM d_user WHERE d_user.sso_org_code = 'ORG001' UNION SELECT id, name FROM d_admin WHERE d_admin.sso_org_code = 'ORG001'
```

## 性能监控

启用性能监控后，可以通过以下方式获取统计信息：

```java
// 获取性能统计
PerformanceMonitor.PerformanceStats stats = PerformanceMonitor.getStats();

// 打印统计信息
PerformanceMonitor.printStats();

// 重置统计信息
PerformanceMonitor.reset();
```

## 缓存管理

```java
// 清理缓存
EnhancedSsoOrgCodeInterceptor.clearCache();

// 获取缓存统计
Map<String, Object> cacheStats = EnhancedSsoOrgCodeInterceptor.getCacheStats();
```

## 测试

运行测试类验证功能：

```java
SqlParserTest test = new SqlParserTest();
test.runAllTests();
```

## 注意事项

1. **兼容性**：确保JSQLParser版本与项目其他依赖兼容
2. **性能**：首次解析SQL会有一定开销，但后续会使用缓存
3. **日志级别**：建议在生产环境中将日志级别设置为INFO或WARN
4. **缓存大小**：根据应用的SQL复杂度调整缓存大小

## 故障排除

1. **解析失败**：检查SQL语法是否正确，JSQLParser是否支持该语法
2. **性能问题**：检查缓存是否启用，考虑增加缓存大小
3. **条件未添加**：检查表名是否在包含列表中，是否在排除列表中

## 升级指南

从原版拦截器升级到增强版：

1. 添加JSQLParser依赖
2. 替换拦截器类名
3. 更新配置文件
4. 测试验证功能
5. 监控性能表现
