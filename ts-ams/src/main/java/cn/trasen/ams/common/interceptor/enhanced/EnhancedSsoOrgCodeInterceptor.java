package cn.trasen.ams.common.interceptor.enhanced;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.StringValue;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.expression.operators.relational.EqualsTo;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.Select;
import net.sf.jsqlparser.statement.select.SelectBody;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.SetOperationList;

import net.sf.jsqlparser.statement.select.FromItem;
import net.sf.jsqlparser.statement.select.SubSelect;
import net.sf.jsqlparser.statement.select.Join;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.plugin.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 增强版MySQL SQL拦截器 - 使用JSQLParser进行专业SQL解析
 * 自动为SELECT语句添加sso_org_code条件，实现数据隔离
 * 
 * 特性：
 * 1. 使用JSQLParser专业SQL解析库，支持复杂SQL语法
 * 2. 正确处理子查询、UNION、复杂JOIN等
 * 3. 性能优化：缓存解析结果，避免重复解析
 * 4. 健壮性：完善的错误处理和回退机制
 * 5. 可配置：支持灵活的表过滤规则
 *
 * <AUTHOR>
 * @date 2025/9/4
 */
@Intercepts({
        @Signature(type = StatementHandler.class, method = "prepare", args = {Connection.class, Integer.class})
})
public class EnhancedSsoOrgCodeInterceptor implements Interceptor {

    private static final Logger log = LoggerFactory.getLogger(EnhancedSsoOrgCodeInterceptor.class);

    // 缓存解析结果，提高性能
    private static final Map<String, SqlParseResult> parseCache = new ConcurrentHashMap<>();
    private static final int MAX_CACHE_SIZE = 1000;

    // 需要排除的表
    private static final Set<String> EXCLUDE_TABLES = new HashSet<>(Arrays.asList(
            "d_category22", "sys_config", "sys_log", "sys_menu", "sys_role", "sys_user"
    ));

    // 需要添加sso_org_code条件的表前缀
    private static final Set<String> INCLUDE_TABLE_PREFIXES = new HashSet<>(Arrays.asList(
            "d_", "m_", "call_", "civil_", "comm_", "cust_", "dept_", "device_", "di_", "dp_",
            "emp_", "gov_", "hr_", "hrms_", "importdata_", "jc_", "kq_", "med_", "new_",
            "political_", "satisfaction_", "scheduling_", "sms_", "t_", "tbl_", "thr_",
            "toa_", "user_", "wf_", "ws_", "zdy_", "zp_", "zt_", "ts_", "c_", "thps_"
    ));

    // 配置属性
    private boolean enableSqlModification = true;
    private boolean enableLogging = true;
    private boolean enableCache = true;

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        long startTime = System.currentTimeMillis();

        if (enableLogging) {
            log.info("=== 增强版MySQL拦截器开始执行 ===");
        }

        try {
            StatementHandler statementHandler = (StatementHandler) invocation.getTarget();
            String originalSql = statementHandler.getBoundSql().getSql();

            if (enableLogging) {
                log.debug("原始SQL: {}", originalSql);
            }

            String modifiedSql = processSql(originalSql);

            if (!originalSql.equals(modifiedSql) && enableSqlModification) {
                applySqlModification(statementHandler, modifiedSql);
            }

            Object result = invocation.proceed();

            if (enableLogging) {
                long executionTime = System.currentTimeMillis() - startTime;
                log.info("=== 增强版MySQL拦截器执行完成，耗时: {}ms ===", executionTime);
            }

            return result;

        } catch (Exception e) {
            log.error("增强版MySQL拦截器执行异常", e);
            throw e;
        }
    }

    /**
     * 处理SQL语句
     */
    private String processSql(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return sql;
        }

        try {
            ThpsUser user = UserInfoHolder.getCurrentUserInfo();
            if (user == null || user.getCorpcode() == null || user.getCorpcode().trim().isEmpty()) {
                if (enableLogging) {
                    log.warn("无法获取用户信息或机构代码为空");
                }
                return sql;
            }

            // 检查缓存
            String cacheKey = generateCacheKey(sql, user.getCorpcode());
            if (enableCache && parseCache.containsKey(cacheKey)) {
                SqlParseResult cachedResult = parseCache.get(cacheKey);
                if (cachedResult.isSuccess()) {
                    if (enableLogging) {
                        log.debug("使用缓存的SQL解析结果");
                    }
                    return cachedResult.getModifiedSql();
                }
            }

            SqlParseResult result = parseAndModifySql(sql, user.getCorpcode());
            
            // 缓存结果
            if (enableCache && parseCache.size() < MAX_CACHE_SIZE) {
                parseCache.put(cacheKey, result);
            }

            if (result.isSuccess()) {
                if (enableLogging && !sql.equals(result.getModifiedSql())) {
                    log.info("SQL修改完成 - 主表: {}, 机构代码: {}", result.getMainTable(), user.getCorpcode());
                    log.debug("修改后SQL: {}", result.getModifiedSql());
                }
                return result.getModifiedSql();
            } else {
                if (enableLogging) {
                    log.warn("SQL解析失败，返回原始SQL: {}", result.getErrorMessage());
                }
                return sql;
            }

        } catch (Exception e) {
            log.error("处理SQL时发生异常", e);
            return sql;
        }
    }

    /**
     * 解析并修改SQL
     */
    private SqlParseResult parseAndModifySql(String sql, String orgCode) {
        try {
            Statement statement = CCJSqlParserUtil.parse(sql);
            
            if (!(statement instanceof Select)) {
                return SqlParseResult.success(sql, null, "非SELECT语句，无需修改");
            }

            Select select = (Select) statement;
            Select selectBody = select.getSelectBody();
            
            boolean modified = processSelectBody(selectBody, orgCode);
            
            if (modified) {
                String modifiedSql = select.toString();
                return SqlParseResult.success(modifiedSql, extractMainTable(selectBody), "SQL修改成功");
            } else {
                return SqlParseResult.success(sql, extractMainTable(selectBody), "无需修改");
            }

        } catch (JSQLParserException e) {
            log.warn("JSQLParser解析失败: {}", e.getMessage());
            return SqlParseResult.failure(sql, "JSQLParser解析失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("SQL解析过程中发生异常", e);
            return SqlParseResult.failure(sql, "解析异常: " + e.getMessage());
        }
    }

    /**
     * 处理SelectBody
     */
    private boolean processSelectBody(SelectBody selectBody, String orgCode) {
        if (selectBody instanceof PlainSelect) {
            return processPlainSelect((PlainSelect) selectBody, orgCode);
        } else if (selectBody instanceof SetOperationList) {
            return processSetOperationList((SetOperationList) selectBody, orgCode);
        }
        return false;
    }

    /**
     * 处理普通SELECT语句
     */
    private boolean processPlainSelect(PlainSelect plainSelect, String orgCode) {
        FromItem fromItem = plainSelect.getFromItem();
        if (fromItem == null) {
            return false;
        }

        boolean modified = false;

        // 处理主表
        if (fromItem instanceof Table) {
            Table table = (Table) fromItem;
            if (shouldAddOrgCodeCondition(table.getName())) {
                addOrgCodeCondition(plainSelect, table, orgCode);
                modified = true;
            }
        } else if (fromItem instanceof SubSelect) {
            // 递归处理子查询
            SubSelect subSelect = (SubSelect) fromItem;
            modified = processSelectBody(subSelect.getSelectBody(), orgCode);
        }

        // 处理JOIN表
        List<Join> joins = plainSelect.getJoins();
        if (joins != null) {
            for (Join join : joins) {
                FromItem rightItem = join.getRightItem();
                if (rightItem instanceof Table) {
                    Table table = (Table) rightItem;
                    if (shouldAddOrgCodeCondition(table.getName())) {
                        addOrgCodeConditionToJoin(join, table, orgCode);
                        modified = true;
                    }
                } else if (rightItem instanceof SubSelect) {
                    SubSelect subSelect = (SubSelect) rightItem;
                    if (processSelectBody(subSelect.getSelectBody(), orgCode)) {
                        modified = true;
                    }
                }
            }
        }

        return modified;
    }

    /**
     * 处理UNION等集合操作
     */
    private boolean processSetOperationList(SetOperationList setOperationList, String orgCode) {
        boolean modified = false;
        List<SelectBody> selects = setOperationList.getSelects();
        if (selects != null) {
            for (SelectBody selectBody : selects) {
                if (processSelectBody(selectBody, orgCode)) {
                    modified = true;
                }
            }
        }
        return modified;
    }



    /**
     * 添加机构代码条件到主查询
     */
    private void addOrgCodeCondition(PlainSelect plainSelect, Table table, String orgCode) {
        String tableAlias = table.getAlias() != null ? table.getAlias().getName() : table.getName();
        Column orgCodeColumn = new Column(tableAlias + ".sso_org_code");
        StringValue orgCodeValue = new StringValue(orgCode);
        
        EqualsTo equalsTo = new EqualsTo();
        equalsTo.setLeftExpression(orgCodeColumn);
        equalsTo.setRightExpression(orgCodeValue);

        Expression where = plainSelect.getWhere();
        if (where == null) {
            plainSelect.setWhere(equalsTo);
        } else {
            // 检查是否已经包含sso_org_code条件
            if (!containsOrgCodeCondition(where)) {
                AndExpression andExpression = new AndExpression(equalsTo, where);
                plainSelect.setWhere(andExpression);
            }
        }
    }

    /**
     * 添加机构代码条件到JOIN
     */
    private void addOrgCodeConditionToJoin(Join join, Table table, String orgCode) {
        String tableAlias = table.getAlias() != null ? table.getAlias().getName() : table.getName();
        Column orgCodeColumn = new Column(tableAlias + ".sso_org_code");
        StringValue orgCodeValue = new StringValue(orgCode);

        EqualsTo equalsTo = new EqualsTo();
        equalsTo.setLeftExpression(orgCodeColumn);
        equalsTo.setRightExpression(orgCodeValue);

        Expression onExpression = join.getOnExpression();
        if (onExpression == null) {
            join.setOnExpression(equalsTo);
        } else {
            if (!containsOrgCodeCondition(onExpression)) {
                AndExpression andExpression = new AndExpression(equalsTo, onExpression);
                join.setOnExpression(andExpression);
            }
        }
    }

    /**
     * 检查表达式中是否已包含sso_org_code条件
     */
    private boolean containsOrgCodeCondition(Expression expression) {
        if (expression == null) {
            return false;
        }

        String expressionStr = expression.toString().toLowerCase();
        return expressionStr.contains("sso_org_code");
    }

    /**
     * 提取主表名
     */
    private String extractMainTable(SelectBody selectBody) {
        if (selectBody instanceof PlainSelect) {
            PlainSelect plainSelect = (PlainSelect) selectBody;
            FromItem fromItem = plainSelect.getFromItem();
            if (fromItem instanceof Table) {
                return ((Table) fromItem).getName();
            }
        }
        return null;
    }

    /**
     * 判断是否需要添加机构代码条件
     */
    private boolean shouldAddOrgCodeCondition(String tableName) {
        if (tableName == null || tableName.trim().isEmpty()) {
            return false;
        }

        String pureTableName = getPureTableName(tableName);

        if (EXCLUDE_TABLES.contains(pureTableName.toLowerCase())) {
            return false;
        }

        return INCLUDE_TABLE_PREFIXES.stream()
                .anyMatch(prefix -> pureTableName.toLowerCase().startsWith(prefix.toLowerCase()));
    }

    /**
     * 获取纯表名（去除schema和引号）
     */
    private String getPureTableName(String tableName) {
        if (tableName == null || tableName.trim().isEmpty()) {
            return tableName;
        }

        String pureTableName = tableName;

        // 去除schema前缀
        if (tableName.contains(".")) {
            String[] parts = tableName.split("\\.");
            pureTableName = parts[parts.length - 1];
        }

        // 去除引号
        if (pureTableName.startsWith("`") && pureTableName.endsWith("`")) {
            pureTableName = pureTableName.substring(1, pureTableName.length() - 1);
        }
        if (pureTableName.startsWith("\"") && pureTableName.endsWith("\"")) {
            pureTableName = pureTableName.substring(1, pureTableName.length() - 1);
        }

        return pureTableName;
    }

    /**
     * 生成缓存键
     */
    private String generateCacheKey(String sql, String orgCode) {
        return sql.hashCode() + "_" + orgCode;
    }

    /**
     * 应用SQL修改
     */
    private void applySqlModification(StatementHandler statementHandler, String modifiedSql) {
        try {
            Object boundSql = statementHandler.getBoundSql();

            // 尝试修改sql字段
            try {
                java.lang.reflect.Field sqlField = boundSql.getClass().getDeclaredField("sql");
                sqlField.setAccessible(true);
                sqlField.set(boundSql, modifiedSql);
                if (enableLogging) {
                    log.info("SQL修改成功");
                }
                return;
            } catch (Exception e) {
                log.debug("修改sql字段失败，尝试sqlSource字段");
            }

            // 尝试修改sqlSource字段
            try {
                java.lang.reflect.Field sqlSourceField = boundSql.getClass().getDeclaredField("sqlSource");
                sqlSourceField.setAccessible(true);
                sqlSourceField.set(boundSql, modifiedSql);
                if (enableLogging) {
                    log.info("通过sqlSource字段修改成功");
                }
                return;
            } catch (Exception e) {
                log.warn("修改sqlSource字段失败: {}", e.getMessage());
            }

            log.error("所有SQL修改方法都失败了");

        } catch (Exception e) {
            log.error("应用SQL修改失败", e);
        }
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
        if (properties != null) {
            String sqlModificationProp = properties.getProperty("enableSqlModification");
            if (sqlModificationProp != null) {
                this.enableSqlModification = Boolean.parseBoolean(sqlModificationProp);
            }

            String loggingProp = properties.getProperty("enableLogging");
            if (loggingProp != null) {
                this.enableLogging = Boolean.parseBoolean(loggingProp);
            }

            String cacheProp = properties.getProperty("enableCache");
            if (cacheProp != null) {
                this.enableCache = Boolean.parseBoolean(cacheProp);
            }

            log.info("增强版多机构拦截器配置 - 启用SQL修改: {}, 启用日志: {}, 启用缓存: {}",
                    enableSqlModification, enableLogging, enableCache);
        }
    }

    /**
     * 清理缓存
     */
    public static void clearCache() {
        parseCache.clear();
    }

    /**
     * 获取缓存统计信息
     */
    public static Map<String, Object> getCacheStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("cacheSize", parseCache.size());
        stats.put("maxCacheSize", MAX_CACHE_SIZE);
        return stats;
    }
}