package cn.trasen.ams.common.interceptor.enhanced;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Set;

/**
 * 拦截器配置类
 * 
 * <AUTHOR>
 * @date 2025/9/4
 */
@Component
@ConfigurationProperties(prefix = "trasen.interceptor.org-code")
public class InterceptorConfig {
    
    /**
     * 是否启用SQL修改
     */
    private boolean enableSqlModification = true;
    
    /**
     * 是否启用日志
     */
    private boolean enableLogging = true;
    
    /**
     * 是否启用缓存
     */
    private boolean enableCache = true;
    
    /**
     * 缓存最大大小
     */
    private int maxCacheSize = 1000;
    
    /**
     * 需要排除的表
     */
    private Set<String> excludeTables = new HashSet<>();
    
    /**
     * 需要包含的表前缀
     */
    private Set<String> includeTablePrefixes = new HashSet<>();
    
    /**
     * 机构代码字段名
     */
    private String orgCodeField = "sso_org_code";
    
    /**
     * 是否启用性能监控
     */
    private boolean enablePerformanceMonitoring = false;
    
    /**
     * 性能监控阈值（毫秒）
     */
    private long performanceThreshold = 100;
    
    public InterceptorConfig() {
        // 初始化默认排除表
        excludeTables.add("d_category22");
        excludeTables.add("sys_config");
        excludeTables.add("sys_log");
        excludeTables.add("sys_menu");
        excludeTables.add("sys_role");
        excludeTables.add("sys_user");
        
        // 初始化默认包含表前缀
        includeTablePrefixes.add("d_");
        includeTablePrefixes.add("m_");
        includeTablePrefixes.add("call_");
        includeTablePrefixes.add("civil_");
        includeTablePrefixes.add("comm_");
        includeTablePrefixes.add("cust_");
        includeTablePrefixes.add("dept_");
        includeTablePrefixes.add("device_");
        includeTablePrefixes.add("di_");
        includeTablePrefixes.add("dp_");
        includeTablePrefixes.add("emp_");
        includeTablePrefixes.add("gov_");
        includeTablePrefixes.add("hr_");
        includeTablePrefixes.add("hrms_");
        includeTablePrefixes.add("importdata_");
        includeTablePrefixes.add("jc_");
        includeTablePrefixes.add("kq_");
        includeTablePrefixes.add("med_");
        includeTablePrefixes.add("new_");
        includeTablePrefixes.add("political_");
        includeTablePrefixes.add("satisfaction_");
        includeTablePrefixes.add("scheduling_");
        includeTablePrefixes.add("sms_");
        includeTablePrefixes.add("t_");
        includeTablePrefixes.add("tbl_");
        includeTablePrefixes.add("thr_");
        includeTablePrefixes.add("toa_");
        includeTablePrefixes.add("user_");
        includeTablePrefixes.add("wf_");
        includeTablePrefixes.add("ws_");
        includeTablePrefixes.add("zdy_");
        includeTablePrefixes.add("zp_");
        includeTablePrefixes.add("zt_");
        includeTablePrefixes.add("ts_");
        includeTablePrefixes.add("c_");
        includeTablePrefixes.add("thps_");
    }
    
    // Getters and Setters
    public boolean isEnableSqlModification() {
        return enableSqlModification;
    }
    
    public void setEnableSqlModification(boolean enableSqlModification) {
        this.enableSqlModification = enableSqlModification;
    }
    
    public boolean isEnableLogging() {
        return enableLogging;
    }
    
    public void setEnableLogging(boolean enableLogging) {
        this.enableLogging = enableLogging;
    }
    
    public boolean isEnableCache() {
        return enableCache;
    }
    
    public void setEnableCache(boolean enableCache) {
        this.enableCache = enableCache;
    }
    
    public int getMaxCacheSize() {
        return maxCacheSize;
    }
    
    public void setMaxCacheSize(int maxCacheSize) {
        this.maxCacheSize = maxCacheSize;
    }
    
    public Set<String> getExcludeTables() {
        return excludeTables;
    }
    
    public void setExcludeTables(Set<String> excludeTables) {
        this.excludeTables = excludeTables;
    }
    
    public Set<String> getIncludeTablePrefixes() {
        return includeTablePrefixes;
    }
    
    public void setIncludeTablePrefixes(Set<String> includeTablePrefixes) {
        this.includeTablePrefixes = includeTablePrefixes;
    }
    
    public String getOrgCodeField() {
        return orgCodeField;
    }
    
    public void setOrgCodeField(String orgCodeField) {
        this.orgCodeField = orgCodeField;
    }
    
    public boolean isEnablePerformanceMonitoring() {
        return enablePerformanceMonitoring;
    }
    
    public void setEnablePerformanceMonitoring(boolean enablePerformanceMonitoring) {
        this.enablePerformanceMonitoring = enablePerformanceMonitoring;
    }
    
    public long getPerformanceThreshold() {
        return performanceThreshold;
    }
    
    public void setPerformanceThreshold(long performanceThreshold) {
        this.performanceThreshold = performanceThreshold;
    }
}
