# 增强版机构代码拦截器配置示例
# 将此配置添加到您的 application.yml 文件中

trasen:
  interceptor:
    org-code:
      # 是否启用SQL修改功能
      enable-sql-modification: true
      
      # 是否启用详细日志
      enable-logging: true
      
      # 是否启用缓存机制
      enable-cache: true
      
      # 缓存最大大小
      max-cache-size: 1000
      
      # 机构代码字段名
      org-code-field: "sso_org_code"
      
      # 是否启用性能监控
      enable-performance-monitoring: false
      
      # 性能监控阈值（毫秒）
      performance-threshold: 100
      
      # 需要排除的表（这些表不会添加机构代码条件）
      exclude-tables:
        - "sys_config"
        - "sys_log"
        - "sys_menu"
        - "sys_role"
        - "sys_user"
        - "d_category22"
        - "sequence_table"
        - "version_info"
      
      # 需要包含的表前缀（只有这些前缀的表才会添加机构代码条件）
      include-table-prefixes:
        - "d_"          # 字典表
        - "m_"          # 主数据表
        - "t_"          # 业务表
        - "c_"          # 配置表
        - "call_"       # 呼叫相关表
        - "civil_"      # 民政相关表
        - "comm_"       # 通用表
        - "cust_"       # 客户表
        - "dept_"       # 部门表
        - "device_"     # 设备表
        - "di_"         # 数据集成表
        - "dp_"         # 数据处理表
        - "emp_"        # 员工表
        - "gov_"        # 政府相关表
        - "hr_"         # 人力资源表
        - "hrms_"       # 人力资源管理表
        - "importdata_" # 导入数据表
        - "jc_"         # 基础表
        - "kq_"         # 考勤表
        - "med_"        # 医疗表
        - "new_"        # 新增表
        - "political_"  # 政治相关表
        - "satisfaction_" # 满意度表
        - "scheduling_" # 排班表
        - "sms_"        # 短信表
        - "tbl_"        # 表格表
        - "thr_"        # 线程相关表
        - "toa_"        # TOA相关表
        - "user_"       # 用户表
        - "wf_"         # 工作流表
        - "ws_"         # Web服务表
        - "zdy_"        # 自定义表
        - "zp_"         # 招聘表
        - "zt_"         # 状态表
        - "ts_"         # 系统表
        - "thps_"       # THPS相关表

# MyBatis 拦截器配置示例
# 在 mybatis-config.xml 中添加以下配置：
#
# <plugins>
#     <plugin interceptor="cn.trasen.ams.common.interceptor.enhanced.EnhancedSsoOrgCodeInterceptor">
#         <property name="enableSqlModification" value="true"/>
#         <property name="enableLogging" value="true"/>
#         <property name="enableCache" value="true"/>
#     </plugin>
# </plugins>

# 或者在 Spring Boot 中通过 @Bean 配置：
#
# @Configuration
# public class MyBatisConfig {
#     
#     @Bean
#     public EnhancedSsoOrgCodeInterceptor enhancedSsoOrgCodeInterceptor() {
#         EnhancedSsoOrgCodeInterceptor interceptor = new EnhancedSsoOrgCodeInterceptor();
#         Properties properties = new Properties();
#         properties.setProperty("enableSqlModification", "true");
#         properties.setProperty("enableLogging", "true");
#         properties.setProperty("enableCache", "true");
#         interceptor.setProperties(properties);
#         return interceptor;
#     }
# }
